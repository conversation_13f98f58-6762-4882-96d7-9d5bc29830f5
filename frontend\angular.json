{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"indezy-frontend": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular/build:application", "options": {"outputPath": {"base": "dist/indezy-frontend"}, "index": "src/index.html", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["@angular/material/prebuilt-themes/indigo-pink.css", "src/styles.scss"], "scripts": [], "browser": "src/main.ts"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "1mb", "maximumError": "2mb"}, {"type": "anyComponentStyle", "maximumWarning": "10kb", "maximumError": "15kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "indezy-frontend:build:production"}, "development": {"buildTarget": "indezy-frontend:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular/build:extract-i18n", "options": {"buildTarget": "indezy-frontend:build"}}, "test": {"builder": "@angular/build:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["@angular/material/prebuilt-themes/indigo-pink.css", "src/styles.scss"], "scripts": [], "codeCoverage": true, "codeCoverageExclude": ["src/test.ts", "src/polyfills.ts", "**/*.spec.ts"]}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"analytics": false}, "schematics": {"@schematics/angular:component": {"type": "component"}, "@schematics/angular:directive": {"type": "directive"}, "@schematics/angular:service": {"type": "service"}, "@schematics/angular:guard": {"typeSeparator": "."}, "@schematics/angular:interceptor": {"typeSeparator": "."}, "@schematics/angular:module": {"typeSeparator": "."}, "@schematics/angular:pipe": {"typeSeparator": "."}, "@schematics/angular:resolver": {"typeSeparator": "."}}}